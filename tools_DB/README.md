# MySQL Database Synchronization from AWS to Google Cloud Platform

## Project Overview

This project implements a robust data synchronization system to regularly retrieve MySQL database data from an AWS server and store it efficiently in Google Cloud Platform (GCP). The system is designed to handle structured data from a tourism/accommodation management system with tables for accommodations, activities, bookings, and financial data.

## Architecture Decision: Compute Engine vs Cloud Run Functions

### Current Setup Analysis
Your existing system uses:
- Google Cloud Run Functions for data processing
- Google Cloud Storage for data storage  
- Google Cloud Scheduler for automation
- Parquet files for structured data storage
- JSON format for refined trip and finance datasets

### Recommendation: **Use Google Compute Engine**

After analyzing your requirements, **Google Compute Engine is the recommended approach** for the following reasons:

#### Why Compute Engine is Better for Your Use Case:

1. **Database Connection Persistence**
   - Maintains persistent connections to AWS MySQL server
   - Reduces connection overhead for large data transfers
   - Better handling of long-running database operations

2. **Memory and Processing Power**
   - Can handle large datasets without timeout constraints
   - Better memory management for data transformation operations
   - Supports complex data processing workflows

3. **Cost Efficiency for Regular Operations**
   - More cost-effective for scheduled, predictable workloads
   - No cold start delays like Cloud Functions
   - Better resource utilization for batch processing

4. **Flexibility and Control**
   - Full control over the execution environment
   - Can install and configure specific database tools
   - Better error handling and retry mechanisms
   - Support for complex data validation and transformation

#### When Cloud Run Functions Would Be Better:
- Sporadic, event-driven data retrieval
- Simple, lightweight data processing
- Serverless architecture preference
- Minimal maintenance requirements

## Production-Ready VM Instance Setup Guide

### Prerequisites

Before starting, ensure you have:
- Google Cloud Platform account with billing enabled
- Google Cloud SDK installed and configured
- Project with Compute Engine API enabled
- Appropriate IAM permissions (Compute Admin, Service Account Admin)

### Step 1: Project Preparation

#### 1.1 Set Environment Variables

```bash
# Set your project variables
export PROJECT_ID="your-project-id"
export VM_NAME="mysql-sync-vm"
export REGION="europe-west3"  # Germany region
export ZONE="europe-west3-a"  # Frankfurt zone
export SERVICE_ACCOUNT_NAME="mysql-sync-service"
```

#### 1.2 Enable Required APIs

```bash
# Enable necessary Google Cloud APIs
gcloud services enable compute.googleapis.com
gcloud services enable storage.googleapis.com
gcloud services enable logging.googleapis.com
gcloud services enable monitoring.googleapis.com
gcloud services enable cloudresourcemanager.googleapis.com
```

### Step 2: Security Setup (Production-Grade)

#### 2.1 Create Dedicated Service Account

```bash
# Create service account with minimal required permissions
gcloud iam service-accounts create ${SERVICE_ACCOUNT_NAME} \
    --project=${PROJECT_ID} \
    --description="Production service account for MySQL sync operations" \
    --display-name="MySQL Sync Production Service"

# Get the service account email
export SERVICE_ACCOUNT_EMAIL="${SERVICE_ACCOUNT_NAME}@${PROJECT_ID}.iam.gserviceaccount.com"
```

#### 2.2 Assign Minimal Required Permissions

```bash
# Storage permissions for GCS operations
gcloud projects add-iam-policy-binding ${PROJECT_ID} \
    --member="serviceAccount:${SERVICE_ACCOUNT_EMAIL}" \
    --role="roles/storage.objectAdmin"

# Logging permissions for application logs
gcloud projects add-iam-policy-binding ${PROJECT_ID} \
    --member="serviceAccount:${SERVICE_ACCOUNT_EMAIL}" \
    --role="roles/logging.logWriter"

# Monitoring permissions for metrics
gcloud projects add-iam-policy-binding ${PROJECT_ID} \
    --member="serviceAccount:${SERVICE_ACCOUNT_EMAIL}" \
    --role="roles/monitoring.metricWriter"
```

#### 2.3 Create Custom Firewall Rules

```bash
# Create restrictive SSH access (replace with your IP range)
gcloud compute firewall-rules create mysql-sync-ssh-restricted \
    --project=${PROJECT_ID} \
    --direction=INGRESS \
    --priority=1000 \
    --network=default \
    --action=ALLOW \
    --rules=tcp:22 \
    --source-ranges="YOUR_OFFICE_IP/32" \
    --target-tags=mysql-sync-vm \
    --description="Restricted SSH access for MySQL sync VM"

# Create rule for health check (if needed)
gcloud compute firewall-rules create mysql-sync-health-check \
    --project=${PROJECT_ID} \
    --direction=INGRESS \
    --priority=1000 \
    --network=default \
    --action=ALLOW \
    --rules=tcp:8080 \
    --source-ranges="***********/22,**********/16" \
    --target-tags=mysql-sync-vm \
    --description="Health check access for MySQL sync VM"
```

### Step 3: VM Instance Creation (Optimized for Cost and Performance)

#### 3.1 Optimal VM Configuration

**Recommended Specifications for Production:**
- **Machine Type**: e2-standard-2 (2 vCPUs, 8GB RAM) - Cost-optimized
- **Boot Disk**: 10GB SSD persistent disk (as requested)
- **Operating System**: Ubuntu 22.04 LTS (Latest stable)
- **Region**: europe-west3 (Frankfurt, Germany)
- **Zone**: europe-west3-a (Primary zone)

**Estimated Monthly Cost (Germany Region):**
- VM Instance (e2-standard-2): ~€45-55/month
- Storage (10GB SSD): ~€1.70/month
- Network Egress: ~€8-15/month
- **Total Estimated Cost: €55-72/month (~$60-78 USD)**

#### 3.2 Create VM Instance via gcloud CLI

```bash
# Create the production VM instance
gcloud compute instances create ${VM_NAME} \
    --project=${PROJECT_ID} \
    --zone=${ZONE} \
    --machine-type=e2-standard-2 \
    --network-interface=network-tier=PREMIUM,stack-type=IPV4_ONLY,subnet=default \
    --maintenance-policy=MIGRATE \
    --provisioning-model=STANDARD \
    --service-account=${SERVICE_ACCOUNT_EMAIL} \
    --scopes=https://www.googleapis.com/auth/cloud-platform \
    --tags=mysql-sync-vm \
    --create-disk=auto-delete=yes,boot=yes,device-name=${VM_NAME},image=projects/ubuntu-os-cloud/global/images/family/ubuntu-2204-lts,mode=rw,size=10,type=projects/${PROJECT_ID}/zones/${ZONE}/diskTypes/pd-ssd \
    --shielded-secure-boot \
    --shielded-vtpm \
    --shielded-integrity-monitoring \
    --reservation-affinity=any \
    --enable-display-device \
    --metadata=enable-oslogin=TRUE
```

### Step 4: Detailed Console-Based VM Creation Instructions

If you prefer using the Google Cloud Console, follow these detailed steps:

#### 4.1 Navigate to Compute Engine

1. Go to [Google Cloud Console](https://console.cloud.google.com)
2. Select your project
3. Navigate to **Compute Engine** > **VM instances**
4. Click **CREATE INSTANCE**

#### 4.2 Basic Configuration

**Name and Region:**
- **Name**: `mysql-sync-vm`
- **Region**: `europe-west3 (Frankfurt)`
- **Zone**: `europe-west3-a`

#### 4.3 Machine Configuration

**Machine Family:**
- Select **General-purpose**
- **Series**: E2
- **Machine type**: e2-standard-2 (2 vCPU, 8 GB memory)

**CPU Platform:**
- Leave as **Automatic** (cost-optimized)

#### 4.4 Boot Disk Configuration

Click **CHANGE** next to Boot disk:

**Operating System:**
- **Operating system**: Ubuntu
- **Version**: Ubuntu 22.04 LTS
- **Boot disk type**: SSD persistent disk
- **Size**: 10 GB

**Advanced Options:**
- **Deletion rule**: ✅ Delete boot disk when instance is deleted
- **Encryption**: Google-managed encryption key

#### 4.5 Identity and API Access

**Service Account:**
- Select the created service account: `mysql-sync-service@PROJECT_ID.iam.gserviceaccount.com`

**Access Scopes:**
- Select **Allow full access to all Cloud APIs**

#### 4.6 Firewall Configuration

**Firewall:**
- ❌ Allow HTTP traffic
- ❌ Allow HTTPS traffic

**Network Tags:**
- Add tag: `mysql-sync-vm`

### Step 5: Advanced Configuration Options

#### 5.1 Management Settings

Click **MANAGEMENT** tab:

**Availability Policy:**
- **Preemptibility**: Off (for production stability)
- **Automatic restart**: On
- **On host maintenance**: Migrate VM instance

**Metadata:**
```
enable-oslogin: TRUE
startup-script-url: gs://YOUR_BUCKET/startup-script.sh
```

#### 5.2 Security Settings

Click **SECURITY** tab:

**Shielded VM:**
- ✅ Turn on Secure Boot
- ✅ Turn on vTPM
- ✅ Turn on Integrity Monitoring

**SSH Keys:**
- Use OS Login (recommended for production)
- Or add project-wide SSH keys

#### 5.3 Networking Settings

Click **NETWORKING** tab:

**Network Interfaces:**
- **Network**: default
- **Subnet**: default (europe-west3)
- **Primary internal IP**: Ephemeral (Automatic)
- **External IP**: Ephemeral
- **IP forwarding**: Off
- **Network Service Tier**: Premium

**Network Tags:**
- `mysql-sync-vm`

#### 5.4 Sole Tenancy (Optional)

Leave as default (not required for this use case)

### Step 6: Post-Creation Security Hardening

#### 6.1 Connect to VM and Initial Setup

```bash
# Connect to the VM
gcloud compute ssh ${VM_NAME} --zone=${ZONE} --project=${PROJECT_ID}
```

#### 6.2 System Hardening Script

```bash
#!/bin/bash
# Production system hardening script

# Update system packages
sudo apt update && sudo apt upgrade -y

# Install security updates automatically
sudo apt install -y unattended-upgrades
sudo dpkg-reconfigure -plow unattended-upgrades

# Configure firewall
sudo ufw enable
sudo ufw default deny incoming
sudo ufw default allow outgoing
sudo ufw allow ssh
sudo ufw allow 8080/tcp  # For health checks if needed

# Install fail2ban for SSH protection
sudo apt install -y fail2ban
sudo systemctl enable fail2ban
sudo systemctl start fail2ban

# Configure automatic security updates
echo 'Unattended-Upgrade::Automatic-Reboot "false";' | sudo tee -a /etc/apt/apt.conf.d/50unattended-upgrades
echo 'Unattended-Upgrade::Remove-Unused-Dependencies "true";' | sudo tee -a /etc/apt/apt.conf.d/50unattended-upgrades

# Set up log rotation
sudo apt install -y logrotate
```

### Step 7: Production Environment Setup

#### 7.1 Install Required Software

```bash
# Install Python and development tools
sudo apt install -y python3 python3-pip python3-venv python3-dev
sudo apt install -y build-essential libssl-dev libffi-dev
sudo apt install -y mysql-client-core-8.0

# Install monitoring tools
sudo apt install -y htop iotop nethogs
sudo apt install -y curl wget git vim

# Install Google Cloud SDK
curl https://sdk.cloud.google.com | bash
exec -l $SHELL
gcloud init --console-only
```

#### 7.2 Application Directory Structure

```bash
# Create application directory with proper permissions
sudo mkdir -p /opt/mysql-sync
sudo chown $USER:$USER /opt/mysql-sync
cd /opt/mysql-sync

# Create directory structure
mkdir -p {config,src/{database,storage,scheduler,utils},logs,temp,scripts,backups}

# Set proper permissions
chmod 750 /opt/mysql-sync
chmod 700 /opt/mysql-sync/{config,logs,temp,backups}
```

#### 7.3 Python Environment Setup

```bash
# Create Python virtual environment
python3 -m venv venv
source venv/bin/activate

# Upgrade pip and install wheel
pip install --upgrade pip setuptools wheel

# Install production dependencies
pip install pymysql==1.1.0
pip install pandas==2.1.4
pip install pyarrow==14.0.2
pip install google-cloud-storage==2.10.0
pip install google-cloud-logging==3.8.0
pip install schedule==1.2.0
pip install python-dotenv==1.0.0
pip install psutil==5.9.6

# Create requirements.txt
pip freeze > requirements.txt
```

### Step 8: Configuration Management

#### 8.1 Environment Configuration

```bash
# Create environment configuration file
cat > /opt/mysql-sync/config/.env << 'EOF'
# Database Configuration
DB_HOST=your-aws-mysql-host
DB_PORT=3306
DB_USER=your-mysql-user
DB_PASSWORD=your-mysql-password
DB_NAME=your-database-name

# Google Cloud Configuration
GCP_PROJECT_ID=your-project-id
GCS_BUCKET_NAME=your-bucket-name
GCP_REGION=europe-west3

# Application Configuration
LOG_LEVEL=INFO
SYNC_SCHEDULE_HOUR=02
SYNC_SCHEDULE_MINUTE=00
MAX_RETRY_ATTEMPTS=3
BATCH_SIZE=1000

# Security Configuration
SSL_VERIFY=true
CONNECTION_TIMEOUT=30
READ_TIMEOUT=300
EOF

# Secure the configuration file
chmod 600 /opt/mysql-sync/config/.env
```

#### 8.2 Logging Configuration

```bash
# Create logging configuration
cat > /opt/mysql-sync/config/logging.conf << 'EOF'
[loggers]
keys=root,mysql_sync

[handlers]
keys=consoleHandler,fileHandler,rotatingFileHandler

[formatters]
keys=simpleFormatter,detailedFormatter

[logger_root]
level=INFO
handlers=consoleHandler

[logger_mysql_sync]
level=INFO
handlers=fileHandler,rotatingFileHandler
qualname=mysql_sync
propagate=0

[handler_consoleHandler]
class=StreamHandler
level=INFO
formatter=simpleFormatter
args=(sys.stdout,)

[handler_fileHandler]
class=FileHandler
level=INFO
formatter=detailedFormatter
args=('/opt/mysql-sync/logs/mysql_sync.log',)

[handler_rotatingFileHandler]
class=handlers.RotatingFileHandler
level=INFO
formatter=detailedFormatter
args=('/opt/mysql-sync/logs/mysql_sync.log', 'a', 10485760, 5)

[formatter_simpleFormatter]
format=%(asctime)s - %(name)s - %(levelname)s - %(message)s

[formatter_detailedFormatter]
format=%(asctime)s - %(name)s - %(levelname)s - %(module)s - %(funcName)s - %(message)s
EOF
```

### Step 9: System Service Configuration

#### 9.1 Create Systemd Service

```bash
# Create systemd service file
sudo tee /etc/systemd/system/mysql-sync.service << 'EOF'
[Unit]
Description=MySQL Database Sync Service
After=network.target
Wants=network-online.target

[Service]
Type=simple
User=mysql-sync
Group=mysql-sync
WorkingDirectory=/opt/mysql-sync
Environment=PATH=/opt/mysql-sync/venv/bin
ExecStart=/opt/mysql-sync/venv/bin/python /opt/mysql-sync/scripts/sync_runner.py
Restart=always
RestartSec=10
StandardOutput=journal
StandardError=journal
SyslogIdentifier=mysql-sync

# Security settings
NoNewPrivileges=yes
PrivateTmp=yes
ProtectSystem=strict
ProtectHome=yes
ReadWritePaths=/opt/mysql-sync/logs /opt/mysql-sync/temp

[Install]
WantedBy=multi-user.target
EOF
```

#### 9.2 Create Dedicated User

```bash
# Create dedicated user for the service
sudo useradd --system --shell /bin/false --home /opt/mysql-sync mysql-sync
sudo chown -R mysql-sync:mysql-sync /opt/mysql-sync
sudo chmod -R 750 /opt/mysql-sync
```

### Step 10: Monitoring and Alerting Setup

#### 10.1 Health Check Script

```bash
# Create health check script
cat > /opt/mysql-sync/scripts/health_check.py << 'EOF'
#!/usr/bin/env python3
"""
Health check script for MySQL sync service
Returns 0 for healthy, 1 for unhealthy
"""

import sys
import os
import json
import logging
from datetime import datetime, timedelta
from pathlib import Path

def check_last_sync():
    """Check if last sync was successful and recent"""
    try:
        log_file = Path('/opt/mysql-sync/logs/last_sync.json')
        if not log_file.exists():
            return False, "No sync log found"

        with open(log_file, 'r') as f:
            last_sync = json.load(f)

        last_sync_time = datetime.fromisoformat(last_sync['timestamp'])
        if datetime.now() - last_sync_time > timedelta(days=16):  # Allow some buffer
            return False, f"Last sync too old: {last_sync_time}"

        if not last_sync.get('success', False):
            return False, f"Last sync failed: {last_sync.get('error', 'Unknown error')}"

        return True, "Last sync successful"

    except Exception as e:
        return False, f"Health check error: {e}"

def main():
    healthy, message = check_last_sync()

    if healthy:
        print(f"HEALTHY: {message}")
        sys.exit(0)
    else:
        print(f"UNHEALTHY: {message}")
        sys.exit(1)

if __name__ == "__main__":
    main()
EOF

chmod +x /opt/mysql-sync/scripts/health_check.py
```

#### 10.2 Monitoring Cron Jobs

```bash
# Add monitoring cron jobs
(crontab -l 2>/dev/null; echo "*/5 * * * * /opt/mysql-sync/scripts/health_check.py >> /opt/mysql-sync/logs/health_check.log 2>&1") | crontab -
(crontab -l 2>/dev/null; echo "0 6 * * * /opt/mysql-sync/scripts/cleanup_logs.sh >> /opt/mysql-sync/logs/cleanup.log 2>&1") | crontab -
```

### Step 11: Backup and Disaster Recovery

#### 11.1 Automated Backup Script

```bash
# Create backup script
cat > /opt/mysql-sync/scripts/backup_config.sh << 'EOF'
#!/bin/bash
"""
Backup configuration and logs to GCS
"""

BACKUP_DATE=$(date +%Y%m%d_%H%M%S)
BACKUP_DIR="/tmp/mysql-sync-backup-${BACKUP_DATE}"
GCS_BACKUP_PATH="gs://${GCS_BUCKET_NAME}/backups/mysql-sync"

# Create backup directory
mkdir -p ${BACKUP_DIR}

# Backup configuration files
cp -r /opt/mysql-sync/config ${BACKUP_DIR}/
cp /opt/mysql-sync/requirements.txt ${BACKUP_DIR}/
cp -r /opt/mysql-sync/src ${BACKUP_DIR}/
cp -r /opt/mysql-sync/scripts ${BACKUP_DIR}/

# Backup recent logs (last 7 days)
find /opt/mysql-sync/logs -name "*.log" -mtime -7 -exec cp {} ${BACKUP_DIR}/ \;

# Create archive
cd /tmp
tar -czf mysql-sync-backup-${BACKUP_DATE}.tar.gz mysql-sync-backup-${BACKUP_DATE}/

# Upload to GCS
gsutil cp mysql-sync-backup-${BACKUP_DATE}.tar.gz ${GCS_BACKUP_PATH}/

# Cleanup local backup
rm -rf ${BACKUP_DIR}
rm mysql-sync-backup-${BACKUP_DATE}.tar.gz

echo "Backup completed: ${GCS_BACKUP_PATH}/mysql-sync-backup-${BACKUP_DATE}.tar.gz"
EOF

chmod +x /opt/mysql-sync/scripts/backup_config.sh
```

#### 11.2 Log Cleanup Script

```bash
# Create log cleanup script
cat > /opt/mysql-sync/scripts/cleanup_logs.sh << 'EOF'
#!/bin/bash
"""
Clean up old log files to manage disk space
"""

LOG_DIR="/opt/mysql-sync/logs"
TEMP_DIR="/opt/mysql-sync/temp"

# Remove logs older than 30 days
find ${LOG_DIR} -name "*.log" -mtime +30 -delete
find ${LOG_DIR} -name "*.log.*" -mtime +30 -delete

# Remove temporary files older than 1 day
find ${TEMP_DIR} -type f -mtime +1 -delete

# Remove empty directories
find ${LOG_DIR} -type d -empty -delete
find ${TEMP_DIR} -type d -empty -delete

echo "Log cleanup completed at $(date)"
EOF

chmod +x /opt/mysql-sync/scripts/cleanup_logs.sh
```

### Step 12: Performance Optimization

#### 12.1 System Optimization

```bash
# Create system optimization script
cat > /opt/mysql-sync/scripts/optimize_system.sh << 'EOF'
#!/bin/bash
"""
System optimization for database operations
"""

# Optimize network settings for database connections
echo 'net.core.rmem_max = 16777216' | sudo tee -a /etc/sysctl.conf
echo 'net.core.wmem_max = 16777216' | sudo tee -a /etc/sysctl.conf
echo 'net.ipv4.tcp_rmem = 4096 65536 16777216' | sudo tee -a /etc/sysctl.conf
echo 'net.ipv4.tcp_wmem = 4096 65536 16777216' | sudo tee -a /etc/sysctl.conf

# Optimize file system settings
echo 'vm.swappiness = 10' | sudo tee -a /etc/sysctl.conf
echo 'vm.dirty_ratio = 15' | sudo tee -a /etc/sysctl.conf
echo 'vm.dirty_background_ratio = 5' | sudo tee -a /etc/sysctl.conf

# Apply settings
sudo sysctl -p

# Set up log rotation for application logs
sudo tee /etc/logrotate.d/mysql-sync << 'LOGROTATE_EOF'
/opt/mysql-sync/logs/*.log {
    daily
    missingok
    rotate 30
    compress
    delaycompress
    notifempty
    create 644 mysql-sync mysql-sync
    postrotate
        systemctl reload mysql-sync || true
    endscript
}
LOGROTATE_EOF

echo "System optimization completed"
EOF

chmod +x /opt/mysql-sync/scripts/optimize_system.sh
sudo /opt/mysql-sync/scripts/optimize_system.sh
```

### Step 13: Security Hardening Checklist

#### 13.1 Final Security Configuration

```bash
# Create security hardening script
cat > /opt/mysql-sync/scripts/security_hardening.sh << 'EOF'
#!/bin/bash
"""
Final security hardening for production deployment
"""

# Disable unused services
sudo systemctl disable apache2 2>/dev/null || true
sudo systemctl disable nginx 2>/dev/null || true
sudo systemctl disable mysql 2>/dev/null || true

# Configure SSH security
sudo sed -i 's/#PermitRootLogin yes/PermitRootLogin no/' /etc/ssh/sshd_config
sudo sed -i 's/#PasswordAuthentication yes/PasswordAuthentication no/' /etc/ssh/sshd_config
sudo sed -i 's/#PubkeyAuthentication yes/PubkeyAuthentication yes/' /etc/ssh/sshd_config
sudo systemctl restart sshd

# Set up automatic security updates
sudo apt install -y unattended-upgrades apt-listchanges
echo 'Unattended-Upgrade::Automatic-Reboot "false";' | sudo tee -a /etc/apt/apt.conf.d/50unattended-upgrades
echo 'Unattended-Upgrade::Remove-Unused-Dependencies "true";' | sudo tee -a /etc/apt/apt.conf.d/50unattended-upgrades
echo 'Unattended-Upgrade::Automatic-Reboot-Time "02:00";' | sudo tee -a /etc/apt/apt.conf.d/50unattended-upgrades

# Configure fail2ban
sudo tee /etc/fail2ban/jail.local << 'FAIL2BAN_EOF'
[DEFAULT]
bantime = 3600
findtime = 600
maxretry = 3

[sshd]
enabled = true
port = ssh
filter = sshd
logpath = /var/log/auth.log
maxretry = 3
bantime = 3600
FAIL2BAN_EOF

sudo systemctl restart fail2ban

# Set file permissions
sudo chmod 700 /opt/mysql-sync/config
sudo chmod 600 /opt/mysql-sync/config/.env
sudo chown -R mysql-sync:mysql-sync /opt/mysql-sync

echo "Security hardening completed"
EOF

chmod +x /opt/mysql-sync/scripts/security_hardening.sh
sudo /opt/mysql-sync/scripts/security_hardening.sh
```

### Step 14: Final Deployment Checklist

#### 14.1 Pre-Production Verification

```bash
# Create deployment verification script
cat > /opt/mysql-sync/scripts/verify_deployment.sh << 'EOF'
#!/bin/bash
"""
Verify deployment is ready for production
"""

echo "=== MySQL Sync Deployment Verification ==="

# Check system resources
echo "1. System Resources:"
echo "   Memory: $(free -h | grep Mem | awk '{print $2}')"
echo "   Disk: $(df -h /opt/mysql-sync | tail -1 | awk '{print $4}') available"
echo "   CPU: $(nproc) cores"

# Check services
echo "2. Services Status:"
systemctl is-active --quiet mysql-sync && echo "   ✓ mysql-sync service: Active" || echo "   ✗ mysql-sync service: Inactive"
systemctl is-active --quiet fail2ban && echo "   ✓ fail2ban: Active" || echo "   ✗ fail2ban: Inactive"

# Check network connectivity
echo "3. Network Connectivity:"
ping -c 1 google.com >/dev/null 2>&1 && echo "   ✓ Internet connectivity: OK" || echo "   ✗ Internet connectivity: Failed"

# Check GCS access
echo "4. GCS Access:"
gsutil ls gs://${GCS_BUCKET_NAME} >/dev/null 2>&1 && echo "   ✓ GCS bucket access: OK" || echo "   ✗ GCS bucket access: Failed"

# Check file permissions
echo "5. File Permissions:"
[ -r /opt/mysql-sync/config/.env ] && echo "   ✓ Configuration file: Readable" || echo "   ✗ Configuration file: Not readable"
[ -w /opt/mysql-sync/logs ] && echo "   ✓ Log directory: Writable" || echo "   ✗ Log directory: Not writable"

# Check Python environment
echo "6. Python Environment:"
/opt/mysql-sync/venv/bin/python --version && echo "   ✓ Python environment: OK"
/opt/mysql-sync/venv/bin/pip list | grep -q pymysql && echo "   ✓ Required packages: Installed" || echo "   ✗ Required packages: Missing"

echo "=== Verification Complete ==="
EOF

chmod +x /opt/mysql-sync/scripts/verify_deployment.sh
```

### Step 15: Cost Optimization Recommendations

#### 15.1 Cost Monitoring Setup

```bash
# Create cost monitoring script
cat > /opt/mysql-sync/scripts/cost_monitor.sh << 'EOF'
#!/bin/bash
"""
Monitor and optimize costs
"""

# Check disk usage
DISK_USAGE=$(df /opt/mysql-sync | tail -1 | awk '{print $5}' | sed 's/%//')
if [ $DISK_USAGE -gt 80 ]; then
    echo "WARNING: Disk usage is ${DISK_USAGE}% - consider cleanup"
fi

# Monitor GCS usage
gsutil du -sh gs://${GCS_BUCKET_NAME} 2>/dev/null | awk '{print "GCS Storage Usage: " $1}'

# Check for old backups
OLD_BACKUPS=$(gsutil ls gs://${GCS_BUCKET_NAME}/backups/ | wc -l)
if [ $OLD_BACKUPS -gt 10 ]; then
    echo "INFO: Consider cleaning up old backups (${OLD_BACKUPS} found)"
fi

echo "Cost monitoring completed at $(date)"
EOF

chmod +x /opt/mysql-sync/scripts/cost_monitor.sh
```

#### 15.2 Additional Cost Optimization Tips

**VM Instance Optimization:**
- Use **Sustained Use Discounts** (automatic for consistent usage)
- Consider **Committed Use Discounts** for 1-3 year commitments (up to 57% savings)
- Use **Preemptible instances** for non-critical development/testing (up to 80% savings)

**Storage Optimization:**
- Use **Standard storage** for frequently accessed data
- Use **Nearline storage** for monthly access patterns
- Use **Coldline storage** for quarterly access patterns
- Implement **lifecycle policies** for automatic data archiving

**Network Optimization:**
- Use **Standard network tier** instead of Premium for cost savings
- Minimize **cross-region data transfer**
- Use **Cloud CDN** for frequently accessed data

### Production Deployment Summary

**Final Configuration:**
- **VM Type**: e2-standard-2 (2 vCPU, 8GB RAM)
- **Disk**: 10GB SSD persistent disk
- **OS**: Ubuntu 22.04 LTS
- **Region**: europe-west3 (Frankfurt, Germany)
- **Estimated Cost**: €55-72/month (~$60-78 USD)

**Security Features:**
- Dedicated service account with minimal permissions
- Shielded VM with Secure Boot, vTPM, and Integrity Monitoring
- Restricted firewall rules
- OS Login enabled
- Fail2ban protection
- Automatic security updates
- Encrypted storage

**Monitoring & Maintenance:**
- Automated health checks
- Log rotation and cleanup
- Backup to GCS
- Performance monitoring
- Cost optimization alerts

**Next Steps:**
1. Run the verification script: `/opt/mysql-sync/scripts/verify_deployment.sh`
2. Test database connectivity
3. Configure application-specific settings
4. Schedule initial sync test
5. Set up monitoring alerts
6. Document operational procedures

### Phase 3: Application Development

#### 3.1 Database Connection Module

```python
"""
MySQL Database Client for AWS Connection
Handles secure connection and data extraction
"""

import pymysql
import pandas as pd
from typing import Dict, List, Optional
import logging
from contextlib import contextmanager

class MySQLClient:
    def __init__(self, config: Dict):
        self.config = config
        self.logger = logging.getLogger(__name__)
    
    @contextmanager
    def get_connection(self):
        """
        Context manager for database connections
        Ensures proper connection cleanup
        """
        connection = None
        try:
            connection = pymysql.connect(
                host=self.config['host'],
                user=self.config['user'],
                password=self.config['password'],
                database=self.config['database'],
                charset='utf8mb4',
                cursorclass=pymysql.cursors.DictCursor,
                connect_timeout=30,
                read_timeout=300,
                write_timeout=300
            )
            yield connection
        except Exception as e:
            self.logger.error(f"Database connection error: {e}")
            raise
        finally:
            if connection:
                connection.close()
    
    def extract_table_data(self, table_name: str, 
                          date_column: Optional[str] = None,
                          start_date: Optional[str] = None,
                          end_date: Optional[str] = None) -> pd.DataFrame:
        """
        Extract data from specified table with optional date filtering
        """
        query = f"SELECT * FROM {table_name}"
        
        if date_column and start_date:
            query += f" WHERE {date_column} >= '{start_date}'"
            if end_date:
                query += f" AND {date_column} <= '{end_date}'"
        
        with self.get_connection() as conn:
            return pd.read_sql(query, conn)
```

#### 3.2 Data Storage and Transformation

```python
"""
Google Cloud Storage Manager
Handles data upload and organization
"""

from google.cloud import storage
import pandas as pd
import json
from datetime import datetime
import os

class GCSManager:
    def __init__(self, bucket_name: str, project_id: str):
        self.bucket_name = bucket_name
        self.project_id = project_id
        self.client = storage.Client(project=project_id)
        self.bucket = self.client.bucket(bucket_name)
    
    def save_as_parquet(self, df: pd.DataFrame, 
                       table_name: str, 
                       sync_date: str) -> str:
        """
        Save DataFrame as Parquet file in GCS
        Organized by table and date
        """
        file_path = f"raw_data/{table_name}/{sync_date}/{table_name}.parquet"
        
        # Save locally first
        local_path = f"/tmp/{table_name}.parquet"
        df.to_parquet(local_path, engine='pyarrow', compression='snappy')
        
        # Upload to GCS
        blob = self.bucket.blob(file_path)
        blob.upload_from_filename(local_path)
        
        # Cleanup local file
        os.remove(local_path)
        
        return f"gs://{self.bucket_name}/{file_path}"
    
    def save_processed_json(self, data: Dict, 
                           data_type: str, 
                           sync_date: str) -> str:
        """
        Save processed data as JSON for trips and finance datasets
        """
        file_path = f"processed_data/{data_type}/{sync_date}/{data_type}_processed.json"
        
        # Save locally first
        local_path = f"/tmp/{data_type}_processed.json"
        with open(local_path, 'w') as f:
            json.dump(data, f, indent=2, default=str)
        
        # Upload to GCS
        blob = self.bucket.blob(file_path)
        blob.upload_from_filename(local_path)
        
        # Cleanup local file
        os.remove(local_path)
        
        return f"gs://{self.bucket_name}/{file_path}"
```

### Phase 4: Scheduling and Automation

#### 4.1 Sync Scheduler

```python
"""
Automated sync scheduler
Handles regular data synchronization
"""

import schedule
import time
from datetime import datetime, timedelta
import logging

class SyncScheduler:
    def __init__(self, mysql_client, gcs_manager):
        self.mysql_client = mysql_client
        self.gcs_manager = gcs_manager
        self.logger = logging.getLogger(__name__)
    
    def full_sync(self):
        """
        Perform full database synchronization
        """
        try:
            sync_date = datetime.now().strftime('%Y-%m-%d')
            self.logger.info(f"Starting full sync for {sync_date}")
            
            # Define tables to sync
            tables_config = {
                'accommodations': {'date_column': 'updated_at'},
                'activities': {'date_column': 'updated_at'},
                'bookings': {'date_column': 'created_at'},
                'payments': {'date_column': 'payment_date'},
                'customers': {'date_column': 'updated_at'}
            }
            
            for table_name, config in tables_config.items():
                self.logger.info(f"Syncing table: {table_name}")
                
                # Extract data
                df = self.mysql_client.extract_table_data(
                    table_name=table_name,
                    date_column=config.get('date_column'),
                    start_date=(datetime.now() - timedelta(days=32)).strftime('%Y-%m-%d')
                )
                
                # Save as Parquet
                parquet_path = self.gcs_manager.save_as_parquet(
                    df, table_name, sync_date
                )
                
                self.logger.info(f"Saved {len(df)} records to {parquet_path}")
            
            # Process and save refined data
            self._process_trips_data(sync_date)
            self._process_finance_data(sync_date)
            
            self.logger.info("Full sync completed successfully")
            
        except Exception as e:
            self.logger.error(f"Sync failed: {e}")
            raise
    
    def _process_trips_data(self, sync_date: str):
        """
        Process and refine trips data into JSON format
        """
        # Implementation for trips data processing
        pass
    
    def _process_finance_data(self, sync_date: str):
        """
        Process and refine finance data into JSON format  
        """
        # Implementation for finance data processing
        pass
    
    def start_scheduler(self):
        """
        Start the scheduled sync operations
        """
        # Schedule twice monthly (1st and 15th)
        schedule.every().month.at("02:00").do(self.full_sync)
        schedule.every(15).days.at("02:00").do(self.full_sync)
        
        self.logger.info("Scheduler started - running twice monthly")
        
        while True:
            schedule.run_pending()
            time.sleep(3600)  # Check every hour
```

## Cost Analysis and Comparison

### Google Compute Engine Approach
**Monthly Costs:**
- VM Instance (e2-standard-2): $52-68
- Storage (50GB SSD): $8
- Network Egress: $10-20
- **Total: $70-96/month**

### Cloud Run Functions Approach  
**Monthly Costs:**
- Function Invocations: $5-15
- Compute Time: $10-25
- Network Egress: $10-20
- **Total: $25-60/month**

### Cost-Benefit Analysis

**Compute Engine Advantages:**
- Better performance for large datasets
- More reliable for complex operations
- Persistent connections reduce overhead
- Full control over execution environment

**Cloud Functions Advantages:**
- Lower cost for simple operations
- Serverless management
- Automatic scaling
- Pay-per-use model

## Data Storage Strategy Recommendations

### Current Approach (Recommended to Continue):
1. **Raw Data**: Store in Parquet format for efficient querying and compression
2. **Processed Data**: Store refined trip and finance data in JSON format
3. **Organization**: Hierarchical structure by date and data type

### Enhanced Storage Strategy:
```
gs://your-bucket/
├── raw_data/
│   ├── accommodations/
│   │   └── 2024-01-15/
│   │       └── accommodations.parquet
│   ├── activities/
│   └── bookings/
├── processed_data/
│   ├── trips/
│   │   └── 2024-01-15/
│   │       ├── trips_summary.json
│   │       └── trips_detailed.json
│   └── finance/
│       └── 2024-01-15/
│           ├── revenue_summary.json
│           └── payment_details.json
└── metadata/
    ├── sync_logs/
    └── data_quality_reports/
```

## Implementation Timeline

### Week 1: Infrastructure Setup
- Create GCP Compute Engine instance
- Configure security and networking
- Install required software and dependencies

### Week 2: Application Development
- Develop MySQL client and data extraction modules
- Create GCS storage and transformation components
- Implement logging and error handling

### Week 3: Scheduler and Automation
- Develop sync scheduler with cron-like functionality
- Create health check and monitoring scripts
- Implement data validation and quality checks

### Week 4: Testing and Deployment
- Perform end-to-end testing with sample data
- Optimize performance and error handling
- Deploy production configuration and monitoring

## Monitoring and Maintenance

### Health Checks
- Database connectivity monitoring
- GCS upload success verification
- Data quality validation
- Resource utilization tracking

### Alerting
- Failed sync notifications
- Database connection issues
- Storage quota warnings
- Performance degradation alerts

### Backup Strategy
- Regular VM snapshots
- Configuration backup to GCS
- Database connection credential rotation
- Disaster recovery procedures

## Next Steps

1. **Approve Architecture**: Confirm Compute Engine approach
2. **Set Up GCP Project**: Create dedicated project for database sync
3. **Configure AWS Access**: Ensure network connectivity to AWS MySQL
4. **Begin Implementation**: Start with Phase 1 infrastructure setup
5. **Iterative Development**: Build and test components incrementally

This comprehensive approach ensures reliable, cost-effective, and scalable database synchronization while maintaining data quality and system reliability.
