# SQL to Parquet Conversion Example

This example demonstrates how to convert SQL database data to Parquet format with transformation logic.

## What This Example Shows

1. **SQL Data Source**: Creates a sample SQLite database with customers and orders
2. **Data Extraction**: Reads data using complex SQL queries with JOINs
3. **Transformation Logic**: Applies business rules and calculations
4. **Parquet Storage**: Saves data in multiple Parquet formats
5. **Data Reading**: Demonstrates how to read back the Parquet files

## Sample Data Structure

### Input SQL Tables:
- **customers**: customer_id, name, email, registration_date, country
- **orders**: order_id, customer_id, product_name, quantity, unit_price, order_date, status

### Transformation Logic Applied:
- Date conversions and calculations
- Business tier classification (Premium/Standard)
- Product categorization
- Customer behavior flags
- Aggregated summaries

### Output Parquet Files:
1. **Complete Dataset**: All transformed data in one file
2. **Partitioned Data**: Data split by country and month
3. **Customer Summary**: Aggregated customer metrics
4. **Product Summary**: Product performance data

## How to Run

```bash
# Install dependencies
pip install -r requirements_example.txt

# Run the example
python sql_to_parquet_example.py
```

## Expected Output

The script will:
1. Create sample SQL data
2. Apply transformations
3. Save multiple Parquet files
4. Demonstrate reading the data back
5. Show the benefits of Parquet format

## Benefits of Parquet Format

- **Columnar Storage**: Efficient for analytical queries
- **Compression**: Smaller file sizes than CSV/JSON
- **Schema Preservation**: Data types are maintained
- **Fast Filtering**: Query only needed columns/rows
- **Partitioning**: Organize data for better performance

## File Structure After Running

```
parquet_output/
├── customer_orders_complete.parquet
├── customer_summary.parquet
├── product_summary.parquet
└── partitioned/
    ├── country=FRANCE/
    ├── country=GERMANY/
    └── country=USA/
        ├── order_month=2023-06/
        │   └── part-0.parquet
        └── ...
```
