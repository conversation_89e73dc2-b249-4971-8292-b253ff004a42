"""
Simple example of converting SQL data to Pa<PERSON>t format with transformations
This example shows:
1. Reading data from SQL database
2. Applying transformation logic
3. Storing as Parquet files
4. Reading back the final format
"""

import pandas as pd
import sqlite3
import pyarrow as pa
import pyarrow.parquet as pq
from datetime import datetime, timedelta
import os

def create_sample_sql_data():
    """
    Create a sample SQL database with customer orders data
    """
    # Create in-memory SQLite database
    conn = sqlite3.connect(':memory:')
    
    # Create sample tables
    conn.execute('''
        CREATE TABLE customers (
            customer_id INTEGER PRIMARY KEY,
            name TEXT,
            email TEXT,
            registration_date DATE,
            country TEXT
        )
    ''')
    
    conn.execute('''
        CREATE TABLE orders (
            order_id INTEGER PRIMARY KEY,
            customer_id INTEGER,
            product_name TEXT,
            quantity INTEGER,
            unit_price DECIMAL(10,2),
            order_date DATE,
            status TEXT,
            FOREIGN KEY (customer_id) REFERENCES customers (customer_id)
        )
    ''')
    
    customers_data = [
        (1, '<PERSON>', '<EMAIL>', '2023-01-15', 'USA'),
        (2, '<PERSON>', '<EMAIL>', '2023-02-20', 'Germany'),
        (3, '<PERSON> <PERSON>', '<EMAIL>', '2023-03-10', 'France'),
        (4, 'Alice Brown', '<EMAIL>', '2023-04-05', 'USA'),
        (5, 'Charlie Wilson', '<EMAIL>', '2023-05-12', 'Germany')
    ]
    
    orders_data = [
        (101, 1, 'Laptop', 1, 999.99, '2023-06-01', 'completed'),
        (102, 1, 'Mouse', 2, 25.50, '2023-06-01', 'completed'),
        (103, 2, 'Keyboard', 1, 75.00, '2023-06-02', 'completed'),
        (104, 3, 'Monitor', 1, 299.99, '2023-06-03', 'pending'),
        (105, 2, 'Laptop', 1, 999.99, '2023-06-04', 'completed'),
        (106, 4, 'Tablet', 1, 399.99, '2023-06-05', 'completed'),
        (107, 5, 'Phone', 1, 699.99, '2023-06-06', 'cancelled'),
        (108, 3, 'Headphones', 1, 149.99, '2023-06-07', 'completed')
    ]
    
    conn.executemany('INSERT INTO customers VALUES (?, ?, ?, ?, ?)', customers_data)
    conn.executemany('INSERT INTO orders VALUES (?, ?, ?, ?, ?, ?, ?)', orders_data)
    
    return conn

def extract_and_transform_data(conn):
    """
    Extract data from SQL and apply transformation logic
    """
    print("🔄 Extracting and transforming data...")
    
    # Complex SQL query with joins and aggregations
    sql_query = '''
        SELECT 
            c.customer_id,
            c.name as customer_name,
            c.email,
            c.country,
            c.registration_date,
            o.order_id,
            o.product_name,
            o.quantity,
            o.unit_price,
            o.order_date,
            o.status,
            (o.quantity * o.unit_price) as total_amount
        FROM customers c
        LEFT JOIN orders o ON c.customer_id = o.customer_id
        WHERE o.order_id IS NOT NULL
        ORDER BY c.customer_id, o.order_date
    '''
    
    # Read data into pandas DataFrame
    df = pd.read_sql_query(sql_query, conn)
    
    # Apply transformation logic
    print("🔧 Applying transformations...")
    
    # 1. Convert date columns to proper datetime
    df['registration_date'] = pd.to_datetime(df['registration_date'])
    df['order_date'] = pd.to_datetime(df['order_date'])
    
    # 2. Add calculated columns
    df['days_since_registration'] = (df['order_date'] - df['registration_date']).dt.days
    df['order_month'] = df['order_date'].dt.strftime('%Y-%m')
    df['customer_tier'] = df['total_amount'].apply(lambda x: 'Premium' if x > 500 else 'Standard')
    
    # 3. Clean and standardize data
    df['country'] = df['country'].str.upper()
    df['product_category'] = df['product_name'].apply(categorize_product)
    
    # 4. Add business logic flags
    df['is_high_value'] = df['total_amount'] > 300
    df['is_repeat_customer'] = df.groupby('customer_id')['order_id'].transform('count') > 1
    
    return df

def categorize_product(product_name):
    """
    Simple product categorization logic
    """
    product_name = product_name.lower()
    if product_name in ['laptop', 'tablet', 'phone']:
        return 'Electronics'
    elif product_name in ['mouse', 'keyboard', 'headphones']:
        return 'Accessories'
    elif product_name in ['monitor']:
        return 'Display'
    else:
        return 'Other'

def save_to_parquet(df, output_dir='parquet_output'):
    """
    Save transformed data to Parquet files with partitioning
    """
    print("💾 Saving data to Parquet format...")
    
    # Create output directory
    os.makedirs(output_dir, exist_ok=True)
    
    # Method 1: Save as single Parquet file
    single_file_path = f"{output_dir}/customer_orders_complete.parquet"
    df.to_parquet(single_file_path, index=False, engine='pyarrow')
    print(f"✅ Saved complete dataset to: {single_file_path}")
    
    # Method 2: Save with partitioning by country and month
    partitioned_path = f"{output_dir}/partitioned"
    df.to_parquet(
        partitioned_path, 
        partition_cols=['country', 'order_month'],
        index=False,
        engine='pyarrow'
    )
    print(f"✅ Saved partitioned dataset to: {partitioned_path}")
    
    # Method 3: Save separate files for different business views
    
    # Customer summary
    customer_summary = df.groupby(['customer_id', 'customer_name', 'country']).agg({
        'total_amount': ['sum', 'count', 'mean'],
        'order_date': ['min', 'max'],
        'is_high_value': 'sum'
    }).round(2)
    
    customer_summary.columns = ['total_spent', 'order_count', 'avg_order_value', 
                               'first_order', 'last_order', 'high_value_orders']
    customer_summary = customer_summary.reset_index()
    
    customer_file = f"{output_dir}/customer_summary.parquet"
    customer_summary.to_parquet(customer_file, index=False)
    print(f"✅ Saved customer summary to: {customer_file}")
    
    # Product performance
    product_summary = df.groupby(['product_name', 'product_category']).agg({
        'quantity': 'sum',
        'total_amount': 'sum',
        'customer_id': 'nunique'
    }).round(2)
    
    product_summary.columns = ['total_quantity_sold', 'total_revenue', 'unique_customers']
    product_summary = product_summary.reset_index()
    
    product_file = f"{output_dir}/product_summary.parquet"
    product_summary.to_parquet(product_file, index=False)
    print(f"✅ Saved product summary to: {product_file}")
    
    return {
        'complete': single_file_path,
        'partitioned': partitioned_path,
        'customer_summary': customer_file,
        'product_summary': product_file
    }

def read_parquet_examples(file_paths):
    """
    Demonstrate reading back the Parquet files
    """
    print("\n📖 Reading back Parquet files...")
    
    # Read complete dataset
    print("\n1. Complete Dataset:")
    complete_df = pd.read_parquet(file_paths['complete'])
    print(f"   Shape: {complete_df.shape}")
    print(f"   Columns: {list(complete_df.columns)}")
    print(f"   Memory usage: {complete_df.memory_usage(deep=True).sum() / 1024:.2f} KB")
    
    # Read customer summary
    print("\n2. Customer Summary:")
    customer_df = pd.read_parquet(file_paths['customer_summary'])
    print(customer_df.head())
    
    # Read product summary
    print("\n3. Product Summary:")
    product_df = pd.read_parquet(file_paths['product_summary'])
    print(product_df.head())
    
    # Read partitioned data (specific partition)
    print("\n4. Partitioned Data (Germany orders):")
    try:
        germany_df = pd.read_parquet(
            file_paths['partitioned'], 
            filters=[('country', '=', 'GERMANY')]
        )
        print(f"   Germany orders: {len(germany_df)} records")
        print(germany_df[['customer_name', 'product_name', 'total_amount']].head())
    except Exception as e:
        print(f"   Note: {e}")

def main():
    """
    Main function demonstrating the complete SQL to Parquet workflow
    """
    print("🚀 SQL to Parquet Conversion Example")
    print("=" * 50)
    
    # Step 1: Create sample SQL data
    print("\n1. Creating sample SQL database...")
    conn = create_sample_sql_data()
    
    # Step 2: Extract and transform data
    transformed_df = extract_and_transform_data(conn)
    
    print(f"\n📊 Transformed Data Preview:")
    print(f"Shape: {transformed_df.shape}")
    print(transformed_df.head())
    
    # Step 3: Save to Parquet
    file_paths = save_to_parquet(transformed_df)
    
    # Step 4: Demonstrate reading back
    read_parquet_examples(file_paths)
    
    # Step 5: Show file sizes and benefits
    print("\n📈 Parquet Benefits:")
    print("✅ Columnar storage - efficient for analytics")
    print("✅ Compression - smaller file sizes")
    print("✅ Schema preservation - data types maintained")
    print("✅ Fast filtering - predicate pushdown")
    print("✅ Partitioning - query only relevant data")
    
    # Cleanup
    conn.close()
    print("\n🎉 Example completed successfully!")

if __name__ == "__main__":
    main()
